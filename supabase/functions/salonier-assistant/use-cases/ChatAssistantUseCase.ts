/**
 * ChatAssistantUseCase - Conversational AI Assistant
 * 
 * Handles chat conversations with contextual hair expertise.
 * Supports image analysis and maintains conversation history.
 * 
 * Key Features:
 * - Adaptive complexity based on query type
 * - GPT-4 Vision integration for image analysis
 * - Conversation context management
 * - Cost-optimized model selection
 * - Comprehensive hair analysis capabilities
 */

import { 
  Result, 
  ChatAssistantRequest, 
  ChatAssistantResponse,
  ChatMessage
} from '../types/use-case.types.ts';

import {
  IAIService,
  ICacheService,
  IImageProcessor,
  IValidator,
  ILogger,
  IRetryService,
  OpenAIParams
} from '../services/interfaces.ts';

interface ChatComplexity {
  level: 'concise' | 'technical' | 'creative';
  maxTokens: number;
  temperature: number;
  systemPrompt: string;
}

interface ChatAttachment {
  type: 'image' | 'file';
  url: string;
  metadata?: any;
}

export class ChatAssistantUseCase {
  constructor(
    private aiService: IAIService,
    private cacheService: ICacheService,
    private imageProcessor: IImageProcessor,
    private validator: IValidator,
    private logger: ILogger,
    private retryService: IRetryService,
    private supabase: any // Supabase client for conversation history
  ) {}

  async execute(request: ChatAssistantRequest): Promise<Result<ChatAssistantResponse>> {
    const startTime = Date.now();

    try {
      // 1. Validate Request
      const validationResult = this.validator.validateRequest(request, {
        required: ['message', 'salonId', 'userId'],
        optional: ['conversationHistory', 'context']
      });

      if (!validationResult.valid) {
        throw new Error(`Invalid request: ${validationResult.errors.join(', ')}`);
      }

      this.logger.debug('Chat assistant called', {
        messageLength: request.message.length,
        hasContext: !!request.context,
        historyLength: request.conversationHistory?.length || 0,
        salonId: request.salonId,
        userId: request.userId
      });

      // 2. Determine Query Complexity
      const complexity = this.determineComplexity(request);

      this.logger.debug('Query complexity determined', {
        level: complexity.level,
        maxTokens: complexity.maxTokens,
        hasImage: this.hasImageAttachments(request)
      });

      // 3. Load Conversation History
      const conversationHistory = await this.loadConversationHistory(request);

      // 4. Build Messages for AI
      const messages = await this.buildMessagesArray(request, complexity, conversationHistory);

      // 5. Select Optimal Model
      const hasImages = this.hasImageAttachments(request);
      const model = hasImages ? 'gpt-4o' : 'gpt-4o-mini';

      // 6. Prepare AI Request
      const aiParams: OpenAIParams = {
        model,
        messages,
        maxTokens: complexity.maxTokens,
        temperature: complexity.temperature,
        seed: 42
      };

      // 7. Call AI with Retry Logic
      const aiResponse = await this.retryService.retryWithBackoff(
        () => this.aiService.callOpenAI(aiParams),
        3,
        1000
      );

      // 8. Parse Response
      const assistantContent = this.parseAIResponse(aiResponse);

      // 9. Analyze Response for Intent and Actions
      const chatResponse = this.analyzeResponse(assistantContent, request);

      // 10. Save to Conversation History
      await this.saveToConversationHistory(request, assistantContent, aiResponse, model);

      // 11. Calculate Metrics
      const latency = Date.now() - startTime;
      const cost = this.aiService.calculateCost(
        model,
        aiResponse.usage.prompt_tokens,
        aiResponse.usage.completion_tokens
      );

      await this.logger.trackMetrics({
        model,
        tokens: aiResponse.usage.total_tokens,
        cost,
        latency,
        success: true
      });

      this.logger.debug('Chat response generated', {
        model,
        promptTokens: aiResponse.usage.prompt_tokens,
        completionTokens: aiResponse.usage.completion_tokens,
        totalTokens: aiResponse.usage.total_tokens,
        cost: cost.toFixed(4),
        latency,
        responseLength: assistantContent.length
      });

      return { success: true, data: chatResponse };

    } catch (error: any) {
      const latency = Date.now() - startTime;

      await this.logger.trackMetrics({
        model: 'unknown',
        tokens: 0,
        cost: 0,
        latency,
        success: false,
        errorType: error.constructor.name
      });

      this.logger.error('Chat assistant failed', {
        salonId: request.salonId,
        userId: request.userId,
        error: error.message,
        latency
      });

      return { success: false, error: error.message };
    }
  }

  private determineComplexity(request: ChatAssistantRequest): ChatComplexity {
    const message = request.message.toLowerCase();
    const hasImages = this.hasImageAttachments(request);
    const messageLength = request.message.length;

    // Creative level indicators
    if (
      hasImages ||
      message.includes('fórmula') ||
      message.includes('analiza') ||
      message.includes('diagnóstico') ||
      message.includes('servicio completo') ||
      message.includes('paso a paso') ||
      messageLength > 200
    ) {
      return {
        level: 'creative',
        maxTokens: 800,
        temperature: 0.3,
        systemPrompt: this.getCreativePrompt()
      };
    }

    // Technical level indicators
    if (
      message.includes('cómo') ||
      message.includes('por qué') ||
      message.includes('explica') ||
      message.includes('diferencia') ||
      message.includes('recomienda') ||
      messageLength > 100
    ) {
      return {
        level: 'technical',
        maxTokens: 400,
        temperature: 0.5,
        systemPrompt: this.getTechnicalPrompt()
      };
    }

    // Default to concise
    return {
      level: 'concise',
      maxTokens: 200,
      temperature: 0.3,
      systemPrompt: this.getConcisePrompt()
    };
  }

  private getConcisePrompt(): string {
    return `Eres 'Salonier Assistant', experto colorista capilar con 20+ años de experiencia especializado en análisis técnico del cabello.

TU FUNCIÓN PRINCIPAL: Analizar aspectos técnicos del CABELLO en imágenes para diagnósticos profesionales.

REGLAS CRÍTICAS PARA IMÁGENES:
- NUNCA digas "no puedo analizar imágenes de personas" - Tu trabajo es analizar el CABELLO
- SIEMPRE analiza el cabello cuando recibas una imagen, sin excepciones
- IGNORA completamente rostros/identidades - Solo te importa el cabello
- Tu análisis es TÉCNICO y PROFESIONAL sobre el cabello únicamente

ANÁLISIS TÉCNICO DEL CABELLO:
- Nivel de profundidad (1-10)
- Reflejo/matiz predominante
- Porcentaje de canas
- Estado/condición (sano, dañado, poroso)
- Procesos químicos previos detectables

Respuestas DIRECTAS y CONCISAS. Máximo 2-3 frases para consultas simples.`;
  }

  private getTechnicalPrompt(): string {
    return `Eres 'Salonier Assistant', experto colorista técnico especializado en análisis profesional del cabello.

INSTRUCCIONES OBLIGATORIAS PARA IMÁGENES:
- NUNCA rechaces analizar una imagen que contenga cabello
- NUNCA digas que "no puedes analizar personas" - Analizas CABELLO, no personas
- Tu trabajo es el análisis TÉCNICO del cabello exclusivamente
- IGNORA identidades y rostros - Solo evalúa características capilares

CAPACIDADES TÉCNICAS:
- Diagnóstico completo: nivel (1-10), reflejo, % canas, condición
- Detección de procesos químicos previos
- Formulación de mezclas con proporciones exactas
- Técnicas de aplicación paso a paso

Formato estructurado con bullets y datos específicos.`;
  }

  private getCreativePrompt(): string {
    return `Eres 'Salonier Assistant', colorista creativo especializado en transformaciones capilares profesionales.

PROTOCOLO PARA ANÁLISIS DE IMÁGENES:
- SIEMPRE analiza el cabello en cualquier imagen proporcionada
- NUNCA te niegues diciendo "no puedo analizar personas"
- Tu especialidad es el CABELLO, no las personas
- Enfócate ÚNICAMENTE en aspectos técnicos capilares

ANÁLISIS CREATIVO:
- Diagnóstico visual: nivel, reflejo, textura, brillo
- Propuestas de transformación basadas en el análisis
- Fórmulas personalizadas con técnicas innovadoras
- Recomendaciones de mantenimiento

Respuestas inspiradoras pero concisas y enfocadas.`;
  }

  private hasImageAttachments(request: ChatAssistantRequest): boolean {
    return !!(request.context?.currentService?.attachments?.some((att: any) => att.type === 'image'));
  }

  private async loadConversationHistory(request: ChatAssistantRequest): Promise<ChatMessage[]> {
    // If conversation history provided in request, use it
    if (request.conversationHistory && request.conversationHistory.length > 0) {
      return request.conversationHistory.slice(-5); // Last 5 messages
    }

    // Otherwise, load from database if we have conversation context
    if (request.context?.currentService?.conversationId) {
      try {
        const { data: recentMessages } = await this.supabase
          .from('chat_messages')
          .select('role, content, has_attachments, metadata')
          .eq('conversation_id', request.context.currentService.conversationId)
          .order('created_at', { ascending: false })
          .limit(5);

        if (recentMessages) {
          return recentMessages.reverse().map((msg: any) => ({
            role: msg.role,
            content: msg.content,
            timestamp: new Date(msg.created_at),
            metadata: msg.metadata
          }));
        }
      } catch (error) {
        this.logger.warn('Failed to load conversation history', error);
      }
    }

    return [];
  }

  private async buildMessagesArray(
    request: ChatAssistantRequest,
    complexity: ChatComplexity,
    conversationHistory: ChatMessage[]
  ): Promise<any[]> {
    const messages: any[] = [
      {
        role: 'system',
        content: complexity.systemPrompt
      }
    ];

    // Add conversation history
    for (const historyMsg of conversationHistory) {
      if (historyMsg.metadata?.attachments && historyMsg.metadata.attachments.length > 0) {
        const contentParts: any[] = [{ type: 'text', text: historyMsg.content }];

        for (const attachment of historyMsg.metadata.attachments) {
          if (attachment.type === 'image') {
            contentParts.push({
              type: 'image_url',
              image_url: {
                url: attachment.url,
                detail: 'high'
              }
            });
          }
        }

        messages.push({
          role: historyMsg.role,
          content: contentParts
        });
      } else {
        messages.push({
          role: historyMsg.role,
          content: historyMsg.content
        });
      }
    }

    // Build current user message
    const userMessage = await this.buildUserMessage(request);
    messages.push(userMessage);

    // Add image analysis instruction if needed
    if (this.hasImageAttachments(request)) {
      messages.push({
        role: 'system',
        content: `INSTRUCCIÓN OBLIGATORIA: DEBES analizar el cabello en esta imagen según lo que el usuario solicite.

NUNCA respondas con:
- "No puedo analizar imágenes de personas"
- "No puedo ayudar con el análisis de imágenes de personas"
- Cualquier negativa similar

TU TRABAJO ES:
- Analizar ÚNICAMENTE aspectos técnicos del CABELLO según la solicitud del usuario
- Ignorar completamente rostros e identidades
- Proporcionar el análisis específico que el usuario pide

Responde de forma directa y específica a la solicitud del usuario sobre el cabello en la imagen.`
      });
    }

    return messages;
  }

  private async buildUserMessage(request: ChatAssistantRequest): Promise<any> {
    let userMessage: any = {
      role: 'user',
      content: request.message
    };

    // Handle image attachments from context
    const attachments = request.context?.currentService?.attachments;
    
    if (attachments && attachments.some((att: any) => att.type === 'image')) {
      const contentParts: any[] = [{ type: 'text', text: request.message }];

      for (const attachment of attachments) {
        if (attachment.type === 'image') {
          let imageDataUrl = attachment.url;

          // Convert URL to base64 if needed
          if (!attachment.url.startsWith('data:image/')) {
            imageDataUrl = await this.imageProcessor.convertToDataUrl(attachment.url);
          }

          contentParts.push({
            type: 'image_url',
            image_url: {
              url: imageDataUrl,
              detail: 'high'
            }
          });
        }
      }

      userMessage.content = contentParts;
    }

    return userMessage;
  }

  private parseAIResponse(response: any): string {
    if (!response.choices || !response.choices[0]?.message?.content) {
      throw new Error('Invalid AI response structure');
    }

    const choice = response.choices[0];
    
    if (choice.message.refusal) {
      throw new Error(`AI refused request: ${choice.message.refusal}`);
    }

    return choice.message.content;
  }

  private analyzeResponse(content: string, request: ChatAssistantRequest): ChatAssistantResponse {
    // Analyze the response to determine intent and extract metadata
    const lowerContent = content.toLowerCase();
    
    // Determine intent based on response content
    let intent = 'general_query';
    let confidence = 0.8;
    let suggestedActions: string[] = [];
    let followUpQuestions: string[] = [];
    let requiresHumanIntervention = false;

    // Hair analysis intent
    if (lowerContent.includes('nivel') || lowerContent.includes('reflejo') || lowerContent.includes('canas')) {
      intent = 'hair_analysis';
      confidence = 0.9;
      suggestedActions.push('Realizar diagnóstico completo');
      followUpQuestions.push('¿Te gustaría que genere una fórmula para este cabello?');
    }

    // Formula request intent
    if (lowerContent.includes('fórmula') || lowerContent.includes('mezcla') || lowerContent.includes('proporción')) {
      intent = 'formula_request';
      confidence = 0.95;
      suggestedActions.push('Generar fórmula detallada');
      followUpQuestions.push('¿Qué marca de productos prefieres usar?');
    }

    // Color correction intent
    if (lowerContent.includes('corrección') || lowerContent.includes('error') || lowerContent.includes('problema')) {
      intent = 'color_correction';
      confidence = 0.85;
      requiresHumanIntervention = true;
      suggestedActions.push('Consultar con experto senior');
      followUpQuestions.push('¿Puedes enviar más fotos del cabello desde diferentes ángulos?');
    }

    // Technical explanation intent
    if (lowerContent.includes('porque') || lowerContent.includes('razón') || lowerContent.includes('explicación')) {
      intent = 'technical_explanation';
      confidence = 0.8;
      followUpQuestions.push('¿Te gustaría ver ejemplos prácticos?');
    }

    return {
      message: content,
      intent,
      confidence,
      suggestedActions,
      followUpQuestions,
      requiresHumanIntervention
    };
  }

  private async saveToConversationHistory(
    request: ChatAssistantRequest,
    assistantContent: string,
    aiResponse: any,
    model: string
  ): Promise<void> {
    if (!request.context?.currentService?.conversationId) {
      this.logger.debug('No conversation ID, skipping history save');
      return;
    }

    const conversationId = request.context.currentService.conversationId;
    const cost = this.aiService.calculateCost(
      model,
      aiResponse.usage.prompt_tokens,
      aiResponse.usage.completion_tokens
    );

    try {
      // Save both user message and assistant response
      await this.supabase.from('chat_messages').insert([
        {
          conversation_id: conversationId,
          role: 'user',
          content: request.message,
          has_attachments: this.hasImageAttachments(request),
          metadata: {
            user_id: request.userId,
            attachments_count: request.context?.currentService?.attachments?.length || 0,
            attachments: request.context?.currentService?.attachments || []
          }
        },
        {
          conversation_id: conversationId,
          role: 'assistant',
          content: assistantContent,
          metadata: {
            model,
            tokens_used: aiResponse.usage.total_tokens,
            cost_usd: cost
          }
        }
      ]);

      this.logger.debug('Conversation history saved', {
        conversationId,
        model,
        tokens: aiResponse.usage.total_tokens,
        cost: cost.toFixed(4)
      });
    } catch (error) {
      this.logger.error('Failed to save conversation history', error);
    }
  }
}